package org.thingsboard.server.dao.sql.waterMonitoring;

import com.alibaba.fastjson.JSONObject;
import com.influxdb.query.FluxTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.thingsboard.server.common.data.TenantId;
import org.thingsboard.server.common.data.id.UUIDConverter;
import org.thingsboard.server.dao.client.StationFeignClient;
import org.thingsboard.server.dao.influx.InfluxService;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.util.SqlDao;
import org.thingsboard.server.dao.waterMonitoring.PumpStationService;
import org.thingsboard.server.common.data.page.PageData;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 泵站监控服务实现类
 */
@Slf4j
@Service
@SqlDao
public class PumpStationServiceImpl implements PumpStationService {

    @Autowired
    private PumpStationMapper pumpStationMapper;

    @Autowired
    private InfluxService influxService;

    @Autowired
    private StationFeignClient stationFeignClient;



    // 泵站类型常量
    private static final String PUMP_STATION_TYPE = "泵房";

    @Override
    public Map<String, Object> getMonitorData(List<String> stationIds, String timeGranularity, Long startTime, Long endTime, String pumpType, String tenantId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取泵站列表
            List<StationEntity> pumpStations = getPumpStationList(tenantId);

            // 如果没有指定泵站ID，则使用所有泵站
            if (stationIds == null || stationIds.isEmpty()) {
                stationIds = pumpStations.stream()
                        .map(StationEntity::getId)
                        .collect(Collectors.toList());
            }

            // 如果没有指定时间范围，默认查询最近24小时
            if (startTime == null) {
                startTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000;
            }
            if (endTime == null) {
                endTime = System.currentTimeMillis();
            }

            // 调用IoT服务获取泵站监控数据
            List<Map<String, Object>> pumpWaterData = getPumpWaterDataFromIoT(stationIds, startTime, endTime, tenantId);
            List<Map<String, Object>> processWaterData = getProcessWaterDataFromIoT(stationIds, startTime, endTime, tenantId);
            List<Map<String, Object>> trendData = getTrendDataFromIoT(stationIds, startTime, endTime, timeGranularity, tenantId);

            result.put("pumpWaterData", pumpWaterData);
            result.put("processWaterData", processWaterData);
            result.put("trendData", trendData);

        } catch (Exception e) {
            log.error("获取泵站监控数据失败", e);
            // 返回空数据而不是抛出异常
            result.put("pumpWaterData", new ArrayList<>());
            result.put("processWaterData", new ArrayList<>());
            result.put("trendData", new ArrayList<>());
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getStationDetail(List<String> stationIds, String tenantId) {
        try {
            // 获取泵站列表
            List<StationEntity> pumpStations = getPumpStationList(tenantId);

            // 如果没有指定泵站ID，则使用所有泵站
            if (stationIds == null || stationIds.isEmpty()) {
                stationIds = pumpStations.stream()
                        .map(StationEntity::getId)
                        .collect(Collectors.toList());
            }

            // 调用IoT服务获取泵站详情数据
            return getStationDetailFromIoT(stationIds, tenantId);

        } catch (Exception e) {
            log.error("获取泵站详情失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public void applyScheme(String schemeId, List<String> stationIds, String tenantId) {
        // 应用方案逻辑
        pumpStationMapper.applyScheme(schemeId, stationIds, tenantId);
    }

    /**
     * 获取泵站列表
     */
    private List<StationEntity> getPumpStationList(String tenantId) {
        try {
            // 从站点服务获取泵站类型的站点
            PageData<StationEntity> pageData = stationFeignClient.list(1, 1000, PUMP_STATION_TYPE, "");
            return pageData.getData() != null ? pageData.getData() : new ArrayList<>();
        } catch (Exception e) {
            log.error("获取泵站列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从IoT服务获取泵水量数据
     */
    private List<Map<String, Object>> getPumpWaterDataFromIoT(List<String> stationIds, Long startTime, Long endTime, String tenantId) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 获取泵站列表
            List<StationEntity> pumpStations = getPumpStationList(tenantId);
            Map<String, StationEntity> stationMap = pumpStations.stream()
                    .collect(Collectors.toMap(StationEntity::getId, station -> station));

            for (String stationId : stationIds) {
                StationEntity station = stationMap.get(stationId);
                if (station != null) {
                    Map<String, Object> stationData = new HashMap<>();
                    stationData.put("stationId", stationId);
                    stationData.put("stationName", station.getName());

                    // 模拟泵水量数据，实际应该从IoT服务获取
                    stationData.put("pumpWater", 200 + Math.random() * 100);
                    stationData.put("otherWater", 150 + Math.random() * 80);
                    result.add(stationData);
                }
            }
        } catch (Exception e) {
            log.error("获取泵水量数据失败", e);
        }

        return result;
    }

    /**
     * 从IoT服务获取处理水量数据
     */
    private List<Map<String, Object>> getProcessWaterDataFromIoT(List<String> stationIds, Long startTime, Long endTime, String tenantId) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 获取泵站列表
            List<StationEntity> pumpStations = getPumpStationList(tenantId);
            Map<String, StationEntity> stationMap = pumpStations.stream()
                    .collect(Collectors.toMap(StationEntity::getId, station -> station));

            for (String stationId : stationIds) {
                StationEntity station = stationMap.get(stationId);
                if (station != null) {
                    Map<String, Object> stationData = new HashMap<>();
                    stationData.put("stationId", stationId);
                    stationData.put("stationName", station.getName());

                    // 模拟处理水量数据，实际应该从IoT服务获取
                    stationData.put("processWater", 180 + Math.random() * 90);
                    stationData.put("intermediateWater", 130 + Math.random() * 70);
                    result.add(stationData);
                }
            }
        } catch (Exception e) {
            log.error("获取处理水量数据失败", e);
        }

        return result;
    }

    /**
     * 从IoT服务获取趋势数据
     */
    private List<Map<String, Object>> getTrendDataFromIoT(List<String> stationIds, Long startTime, Long endTime, String timeGranularity, String tenantId) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 获取泵站列表
            List<StationEntity> pumpStations = getPumpStationList(tenantId);
            Map<String, StationEntity> stationMap = pumpStations.stream()
                    .collect(Collectors.toMap(StationEntity::getId, station -> station));

            for (String stationId : stationIds) {
                StationEntity station = stationMap.get(stationId);
                if (station != null) {
                    Map<String, Object> stationData = new HashMap<>();
                    stationData.put("stationId", stationId);
                    stationData.put("stationName", station.getName());

                    // 构建趋势数据
                    List<Map<String, Object>> trends = new ArrayList<>();
                    String[] timePoints = {"00:00", "03:00", "06:00", "09:00", "12:00", "15:00", "18:00", "21:00"};
                    for (String timePoint : timePoints) {
                        Map<String, Object> point = new HashMap<>();
                        point.put("time", timePoint);
                        point.put("value", 100 + Math.random() * 50);
                        trends.add(point);
                    }

                    stationData.put("trends", trends);
                    result.add(stationData);
                }
            }
        } catch (Exception e) {
            log.error("获取趋势数据失败", e);
        }

        return result;
    }

    /**
     * 从IoT服务获取泵站详情数据
     */
    private List<Map<String, Object>> getStationDetailFromIoT(List<String> stationIds, String tenantId) {
        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 获取泵站列表
            List<StationEntity> pumpStations = getPumpStationList(tenantId);
            Map<String, StationEntity> stationMap = pumpStations.stream()
                    .collect(Collectors.toMap(StationEntity::getId, station -> station));

            for (String stationId : stationIds) {
                StationEntity station = stationMap.get(stationId);
                if (station != null) {
                    Map<String, Object> stationDetail = new HashMap<>();
                    stationDetail.put("stationId", stationId);
                    stationDetail.put("stationName", station.getName());

                    // 构建泵的详情数据
                    List<Map<String, Object>> pumps = new ArrayList<>();
                    Map<String, Object> pump = new HashMap<>();
                    pump.put("pumpId", stationId + "_pump_1");
                    pump.put("pumpName", "主泵");
                    pump.put("pumpCode", "P001");
                    pump.put("pumpType", "electric");
                    pump.put("pumpNum", 1);
                    pump.put("companyName", "水务公司");
                    pump.put("performanceParameters", "额定功率: 15kW, 额定流量: 200m³/h");
                    pumps.add(pump);

                    stationDetail.put("pumps", pumps);
                    result.add(stationDetail);
                }
            }
        } catch (Exception e) {
            log.error("获取泵站详情失败", e);
        }

        return result;
    }


}
